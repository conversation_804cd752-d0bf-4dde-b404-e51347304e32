#!/usr/bin/env node

/**
 * Comprehensive Seed Data Generator for Spritely Medical Application
 *
 * This script generates extensive, realistic medical data for all organizations
 * using Faker.js to create a robust demo environment that showcases all features.
 */

import { faker } from '@faker-js/faker';
import fs from 'fs';

// Configuration for realistic healthcare data generation
const CONFIG = {
  organizations: 16,
  facilitiesPerOrg: { min: 1, max: 4 },
  departmentsPerFacility: { min: 3, max: 8 },
  providersPerDept: { min: 2, max: 8 },
  patientsPerProvider: { min: 15, max: 50 },
  appointmentsPerPatient: { min: 2, max: 12 },
  medicalRecordsPerPatient: { min: 1, max: 8 },
  medicationsPerPatient: { min: 0, max: 6 },
  vitalsPerPatient: { min: 3, max: 15 },
  labResultsPerPatient: { min: 1, max: 8 },
  immunizationsPerPatient: { min: 2, max: 12 }
};

// Healthcare-specific data
const ORGANIZATION_TYPES = ['hospital', 'medical_center', 'clinic', 'urgent_care', 'specialty_practice'];
const SPECIALIZATIONS = [
  'family_medicine', 'internal_medicine', 'pediatrics', 'cardiology', 'neurology',
  'orthopedics', 'dermatology', 'psychiatry', 'radiology', 'emergency_medicine',
  'anesthesiology', 'pathology', 'surgery', 'obstetrics_gynecology', 'urology'
];

const DEPARTMENT_TYPES = [
  'primary_care', 'pediatrics', 'cardiology', 'neurology', 'orthopedics',
  'emergency', 'laboratory', 'pharmacy', 'radiology', 'billing', 'administration'
];

const MEDICAL_CONDITIONS = [
  'Hypertension', 'Type 2 Diabetes', 'Coronary Artery Disease', 'Asthma', 'Chronic Kidney Disease',
  'Depression', 'Anxiety Disorder', 'Arthritis', 'COPD', 'Migraine', 'Obesity', 'Hyperlipidemia'
];

const MEDICATIONS = [
  'Lisinopril', 'Metformin', 'Atorvastatin', 'Albuterol', 'Omeprazole',
  'Sertraline', 'Ibuprofen', 'Acetaminophen', 'Insulin', 'Warfarin'
];

const LAB_TESTS = [
  'Complete Blood Count', 'Basic Metabolic Panel', 'Lipid Panel', 'Liver Function Tests',
  'Thyroid Function Tests', 'HbA1c', 'PT/INR', 'Urinalysis', 'Vitamin D', 'PSA'
];

// Utility functions
const randomDate = (start, end) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

const randomElement = (array) => array[Math.floor(Math.random() * array.length)];

const randomInt = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;

const generateUUID = () => faker.string.uuid();

// Data storage
let organizations = [];
let facilities = [];
let departments = [];
let providers = [];
let patients = [];
let appointments = [];
let medicalRecords = [];
let medications = [];
let vitalSigns = [];
let labResults = [];
let immunizations = [];
let claims = [];
let tasks = [];

// Additional data storage for missing tables
let allergies = [];
let careTeamMembers = [];
let clinicalNotes = [];
let conversations = [];
let conversationParticipants = [];
let documents = [];
let messages = [];
let notificationTemplates = [];
let patientAlerts = [];
let patientPortalSettings = [];
let referrals = [];
let orders = [];
let workflows = [];
let teams = [];

// Generate organizations
console.log('Generating organizations...');
for (let i = 0; i < CONFIG.organizations; i++) {
  const orgType = randomElement(ORGANIZATION_TYPES);
  const orgSize = orgType === 'hospital' ? 'large' : orgType === 'medical_center' ? 'medium' : 'small';
  
  organizations.push({
    id: generateUUID(),
    name: `${faker.company.name()} ${orgType === 'hospital' ? 'Hospital' : orgType === 'medical_center' ? 'Medical Center' : 'Clinic'}`,
    type: orgType,
    settings: JSON.stringify({
      timezone: faker.location.timeZone(),
      locale: 'en-US',
      features: ['appointments', 'billing', 'ehr', 'messaging'],
      size: orgSize
    }),
    created_at: randomDate(new Date(2020, 0, 1), new Date(2023, 0, 1)).toISOString(),
    updated_at: new Date().toISOString()
  });
}

// Generate facilities
console.log('Generating facilities...');
organizations.forEach(org => {
  const facilityCount = randomInt(CONFIG.facilitiesPerOrg.min, CONFIG.facilitiesPerOrg.max);
  for (let i = 0; i < facilityCount; i++) {
    facilities.push({
      id: generateUUID(),
      organization_id: org.id,
      name: i === 0 ? `${org.name} Main Campus` : `${org.name} ${faker.location.city()} Branch`,
      type: org.type === 'hospital' ? 'hospital' : org.type === 'medical_center' ? 'medical_center' : 'clinic',
      address: JSON.stringify({
        street: faker.location.streetAddress(),
        city: faker.location.city(),
        state: faker.location.state(),
        zip_code: faker.location.zipCode(),
        country: 'US'
      }),
      contact_info: JSON.stringify({
        phone: faker.phone.number(),
        fax: faker.phone.number(),
        email: `contact@${faker.internet.domainName()}`
      }),
      operating_hours: JSON.stringify({
        monday: '8:00-17:00',
        tuesday: '8:00-17:00',
        wednesday: '8:00-17:00',
        thursday: '8:00-17:00',
        friday: '8:00-17:00',
        saturday: org.type === 'hospital' ? '8:00-17:00' : 'closed',
        sunday: org.type === 'hospital' ? '8:00-17:00' : 'closed'
      }),
      settings: JSON.stringify({
        parking_available: faker.datatype.boolean(),
        wheelchair_accessible: true,
        emergency_services: org.type === 'hospital'
      }),
      created_at: randomDate(new Date(2020, 0, 1), new Date(2023, 0, 1)).toISOString(),
      updated_at: new Date().toISOString()
    });
  }
});

// Generate departments
console.log('Generating departments...');
facilities.forEach(facility => {
  const deptCount = randomInt(CONFIG.departmentsPerFacility.min, CONFIG.departmentsPerFacility.max);
  const selectedDepts = faker.helpers.arrayElements(DEPARTMENT_TYPES, deptCount);
  
  selectedDepts.forEach(deptType => {
    departments.push({
      id: generateUUID(),
      facility_id: facility.id,
      name: deptType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      type: deptType,
      settings: JSON.stringify({
        hours: '8:00-17:00',
        appointment_duration: deptType === 'emergency' ? 15 : 30,
        walk_ins: deptType === 'emergency' || deptType === 'primary_care'
      }),
      created_at: randomDate(new Date(2020, 0, 1), new Date(2023, 0, 1)).toISOString(),
      updated_at: new Date().toISOString()
    });
  });
});

// Generate healthcare providers
console.log('Generating healthcare providers...');
departments.forEach(dept => {
  const providerCount = randomInt(CONFIG.providersPerDept.min, CONFIG.providersPerDept.max);
  for (let i = 0; i < providerCount; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const specialization = dept.type === 'primary_care' ? 'family_medicine' : 
                          dept.type === 'emergency' ? 'emergency_medicine' :
                          randomElement(SPECIALIZATIONS);
    
    providers.push({
      id: generateUUID(),
      organization_id: organizations.find(o => facilities.find(f => f.id === dept.facility_id)?.organization_id === o.id)?.id,
      department_id: dept.id,
      user_id: generateUUID(),
      first_name: firstName,
      last_name: lastName,
      provider_type: 'doctor',
      specialization: specialization,
      license_number: `MD${faker.string.numeric(8)}`,
      role: 'physician',
      specialties: [specialization],
      credentials: JSON.stringify({
        board_certified: true,
        years_experience: randomInt(1, 30),
        languages: ['English'],
        education: `${faker.company.name()} Medical School`
      }),
      schedule_settings: JSON.stringify({
        monday: '9:00-17:00',
        tuesday: '9:00-17:00',
        wednesday: '9:00-17:00',
        thursday: '9:00-17:00',
        friday: '9:00-17:00',
        accepting_patients: faker.datatype.boolean({ probability: 0.8 })
      }),
      permissions: JSON.stringify({
        can_prescribe: true,
        can_order_labs: true,
        can_refer: true
      }),
      created_at: randomDate(new Date(2020, 0, 1), new Date(2023, 0, 1)).toISOString(),
      updated_at: new Date().toISOString()
    });
  }
});

// Generate patients
console.log('Generating patients...');
providers.forEach(provider => {
  const patientCount = randomInt(CONFIG.patientsPerProvider.min, CONFIG.patientsPerProvider.max);
  for (let i = 0; i < patientCount; i++) {
    const birthDate = randomDate(new Date(1940, 0, 1), new Date(2010, 0, 1));
    const age = new Date().getFullYear() - birthDate.getFullYear();
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    
    patients.push({
      id: generateUUID(),
      organization_id: provider.organization_id,
      user_id: generateUUID(),
      first_name: firstName,
      last_name: lastName,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${faker.internet.email()}`,
      phone: faker.phone.number(),
      date_of_birth: birthDate.toISOString().split('T')[0],
      gender: randomElement(['male', 'female', 'other']),
      address: faker.location.streetAddress(),
      city: faker.location.city(),
      state: faker.location.state(),
      zip_code: faker.location.zipCode(),
      emergency_contact: JSON.stringify({
        name: faker.person.fullName(),
        phone: faker.phone.number(),
        relationship: randomElement(['spouse', 'parent', 'child', 'sibling', 'friend'])
      }),
      insurance_info: JSON.stringify({
        provider: randomElement(['Blue Cross', 'Aetna', 'Cigna', 'UnitedHealth', 'Medicare']),
        policy_number: faker.string.alphanumeric(12),
        group_number: faker.string.alphanumeric(8)
      }),
      metadata: JSON.stringify({
        primary_care_provider: provider.id,
        preferred_language: 'English',
        age: age,
        medical_history: faker.helpers.arrayElements(MEDICAL_CONDITIONS, randomInt(0, 3))
      }),
      created_at: randomDate(new Date(2020, 0, 1), new Date(2023, 0, 1)).toISOString(),
      updated_at: new Date().toISOString()
    });
  }
});

// Generate appointments
console.log('Generating appointments...');
patients.forEach(patient => {
  const appointmentCount = randomInt(CONFIG.appointmentsPerPatient.min, CONFIG.appointmentsPerPatient.max);
  for (let i = 0; i < appointmentCount; i++) {
    const provider = providers.find(p => p.organization_id === patient.organization_id) || providers[0];
    const appointmentDate = randomDate(new Date(2023, 0, 1), new Date(2024, 11, 31));
    const status = appointmentDate < new Date() ? 
      randomElement(['completed', 'no_show', 'cancelled']) : 
      randomElement(['scheduled', 'checked_in']);
    
    appointments.push({
      id: generateUUID(),
      patient_id: patient.id,
      provider_id: provider.id,
      organization_id: patient.organization_id,
      department_id: provider.department_id,
      appointment_date: appointmentDate.toISOString(),
      duration_minutes: randomInt(15, 60),
      status: status,
      reason: randomElement([
        'Annual Physical', 'Follow-up Visit', 'Routine Checkup', 'Consultation',
        'Vaccination', 'Lab Results Review', 'Symptom Evaluation'
      ]),
      notes: faker.lorem.sentence(),
      created_at: randomDate(appointmentDate, new Date()).toISOString(),
      updated_at: new Date().toISOString()
    });
  }
});

// Generate medical records
console.log('Generating medical records...');
patients.forEach(patient => {
  const recordCount = randomInt(CONFIG.medicalRecordsPerPatient.min, CONFIG.medicalRecordsPerPatient.max);
  for (let i = 0; i < recordCount; i++) {
    const provider = providers.find(p => p.organization_id === patient.organization_id) || providers[0];
    const visitDate = randomDate(new Date(2023, 0, 1), new Date());
    const conditions = faker.helpers.arrayElements(MEDICAL_CONDITIONS, randomInt(1, 3));
    
    medicalRecords.push({
      id: generateUUID(),
      patient_id: patient.id,
      provider_id: provider.id,
      organization_id: patient.organization_id,
      department_id: provider.department_id,
      visit_date: visitDate.toISOString(),
      chief_complaint: randomElement([
        'Chest pain', 'Shortness of breath', 'Abdominal pain', 'Headache',
        'Fatigue', 'Joint pain', 'Skin rash', 'Cough', 'Fever'
      ]),
      diagnosis: conditions,
      treatment_plan: `${faker.lorem.sentence()} Follow up in ${randomInt(1, 6)} weeks.`,
      notes: faker.lorem.paragraph(),
      attachments: JSON.stringify({
        files: [],
        images: [],
        lab_reports: []
      }),
      created_at: visitDate.toISOString(),
      updated_at: new Date().toISOString()
    });
  }
});

// Generate medications
console.log('Generating medications...');
patients.forEach(patient => {
  const medicationCount = randomInt(CONFIG.medicationsPerPatient.min, CONFIG.medicationsPerPatient.max);
  for (let i = 0; i < medicationCount; i++) {
    const provider = providers.find(p => p.organization_id === patient.organization_id) || providers[0];
    const startDate = randomDate(new Date(2023, 0, 1), new Date());
    const endDate = faker.datatype.boolean({ probability: 0.3 }) ? 
      randomDate(startDate, new Date(2024, 11, 31)) : null;
    
    medications.push({
      id: generateUUID(),
      patient_id: patient.id,
      provider_id: provider.id,
      medication_name: randomElement(MEDICATIONS),
      dosage: `${randomInt(5, 100)}mg`,
      frequency: randomElement(['Once daily', 'Twice daily', 'Three times daily', 'As needed']),
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate?.toISOString().split('T')[0] || null,
      instructions: faker.lorem.sentence(),
      active: endDate ? endDate > new Date() : true,
      created_at: startDate.toISOString(),
      updated_at: new Date().toISOString()
    });
  }
});

// Generate vital signs
console.log('Generating vital signs...');
patients.forEach(patient => {
  const vitalsCount = randomInt(CONFIG.vitalsPerPatient.min, CONFIG.vitalsPerPatient.max);
  for (let i = 0; i < vitalsCount; i++) {
    const provider = providers.find(p => p.organization_id === patient.organization_id) || providers[0];
    const recordedAt = randomDate(new Date(2023, 0, 1), new Date());
    const age = new Date().getFullYear() - new Date(patient.date_of_birth).getFullYear();
    
    // Age-appropriate vital signs
    const systolic = age < 18 ? randomInt(90, 110) : randomInt(110, 140);
    const diastolic = age < 18 ? randomInt(60, 75) : randomInt(70, 90);
    const heartRate = age < 18 ? randomInt(80, 120) : randomInt(60, 100);
    const temperature = randomInt(970, 990) / 10; // 97.0-99.0°F
    const weight = age < 18 ? randomInt(30, 80) : randomInt(120, 250);
    const height = age < 18 ? randomInt(40, 70) : randomInt(60, 78);
    const bmi = (weight * 703) / (height * height);
    
    vitalSigns.push({
      id: generateUUID(),
      patient_id: patient.id,
      recorded_by: provider.id,
      recorded_at: recordedAt.toISOString(),
      blood_pressure_systolic: systolic,
      blood_pressure_diastolic: diastolic,
      heart_rate: heartRate,
      respiratory_rate: randomInt(12, 20),
      temperature: temperature,
      temperature_unit: 'F',
      oxygen_saturation: randomInt(95, 100),
      height: height,
      weight: weight,
      bmi: Math.round(bmi * 10) / 10,
      pain_level: randomInt(0, 10),
      notes: faker.datatype.boolean({ probability: 0.3 }) ? faker.lorem.sentence() : null,
      metadata: JSON.stringify({
        measurement_method: 'digital',
        position: 'sitting'
      }),
      created_at: recordedAt.toISOString(),
      updated_at: new Date().toISOString()
    });
  }
});

// Generate lab results
console.log('Generating lab results...');
patients.forEach(patient => {
  const labCount = randomInt(CONFIG.labResultsPerPatient.min, CONFIG.labResultsPerPatient.max);
  for (let i = 0; i < labCount; i++) {
    const provider = providers.find(p => p.organization_id === patient.organization_id) || providers[0];
    const testDate = randomDate(new Date(2023, 0, 1), new Date());
    const testName = randomElement(LAB_TESTS);
    
    // Generate realistic lab values based on test type
    let results = {};
    let normalRange = {};
    
    switch (testName) {
      case 'Complete Blood Count':
        results = {
          wbc: randomInt(4000, 11000),
          rbc: (randomInt(450, 550) / 100).toFixed(2),
          hemoglobin: (randomInt(120, 160) / 10).toFixed(1),
          hematocrit: randomInt(36, 48)
        };
        normalRange = {
          wbc: '4,000-11,000',
          rbc: '4.5-5.5',
          hemoglobin: '12.0-16.0',
          hematocrit: '36-48'
        };
        break;
      case 'Basic Metabolic Panel':
        results = {
          glucose: randomInt(70, 100),
          sodium: randomInt(136, 145),
          potassium: (randomInt(35, 50) / 10).toFixed(1),
          chloride: randomInt(98, 107),
          bun: randomInt(7, 20),
          creatinine: (randomInt(6, 12) / 10).toFixed(1)
        };
        normalRange = {
          glucose: '70-100',
          sodium: '136-145',
          potassium: '3.5-5.0',
          chloride: '98-107',
          bun: '7-20',
          creatinine: '0.6-1.2'
        };
        break;
      default:
        results = { value: randomInt(10, 100) };
        normalRange = { value: '10-100' };
    }
    
    labResults.push({
      id: generateUUID(),
      patient_id: patient.id,
      provider_id: provider.id,
      test_name: testName,
      test_date: testDate.toISOString(),
      results: JSON.stringify(results),
      normal_range: JSON.stringify(normalRange),
      notes: faker.datatype.boolean({ probability: 0.2 }) ? faker.lorem.sentence() : null,
      created_at: testDate.toISOString(),
      updated_at: new Date().toISOString()
    });
  }
});

// Generate immunizations
console.log('Generating immunizations...');
patients.forEach(patient => {
  const immunizationCount = randomInt(CONFIG.immunizationsPerPatient.min, CONFIG.immunizationsPerPatient.max);
  const vaccines = [
    'COVID-19', 'Influenza', 'Pneumococcal', 'Hepatitis B', 'MMR', 
    'Tdap', 'HPV', 'Varicella', 'Meningococcal', 'Shingles'
  ];
  
  for (let i = 0; i < immunizationCount; i++) {
    const provider = providers.find(p => p.organization_id === patient.organization_id) || providers[0];
    const administeredDate = randomDate(new Date(2020, 0, 1), new Date());
    const vaccine = randomElement(vaccines);
    
    immunizations.push({
      id: generateUUID(),
      patient_id: patient.id,
      administered_by: provider.id,
      vaccine_name: vaccine,
      vaccine_code: `CVX${faker.string.numeric(3)}`,
      manufacturer: randomElement(['Pfizer', 'Moderna', 'Johnson & Johnson', 'GSK', 'Merck']),
      lot_number: faker.string.alphanumeric(8).toUpperCase(),
      administered_date: administeredDate.toISOString().split('T')[0],
      expiration_date: randomDate(administeredDate, new Date(2025, 11, 31)).toISOString().split('T')[0],
      site: randomElement(['left_arm', 'right_arm', 'left_thigh', 'right_thigh']),
      route: randomElement(['intramuscular', 'subcutaneous', 'oral']),
      dosage: '0.5ml',
      notes: faker.datatype.boolean({ probability: 0.1 }) ? faker.lorem.sentence() : null,
             metadata: JSON.stringify({
         reaction: faker.datatype.boolean({ probability: 0.05 }) ? 'mild soreness' : 'none',
         next_due: vaccine === 'Influenza' ? 'annually' : null
       }),
      created_at: administeredDate.toISOString(),
      updated_at: new Date().toISOString()
    });
  }
});

// Generate claims
console.log('Generating claims...');
appointments.filter(apt => apt.status === 'completed').forEach(appointment => {
  const patient = patients.find(p => p.id === appointment.patient_id);
  const provider = providers.find(p => p.id === appointment.provider_id);
  const serviceDate = new Date(appointment.appointment_date);
  
  claims.push({
    id: generateUUID(),
    patient_id: patient.id,
    provider_id: provider.id,
    insurance_provider_id: generateUUID(),
    service_date: serviceDate.toISOString().split('T')[0],
    billing_codes: JSON.stringify({
      procedure_codes: [`99${randomInt(201, 215)}`],
      diagnosis_codes: [`M${randomInt(10, 99)}.${randomInt(1, 9)}`],
      modifier_codes: []
    }),
    status: randomElement(['submitted', 'approved', 'paid', 'denied', 'pending']),
    amount: randomInt(150, 800),
    submitted_at: randomDate(serviceDate, new Date()).toISOString(),
    processed_at: faker.datatype.boolean({ probability: 0.7 }) ? 
      randomDate(serviceDate, new Date()).toISOString() : null,
    metadata: JSON.stringify({
      insurance_payment: randomInt(100, 600),
      patient_responsibility: randomInt(20, 150),
      copay: randomInt(15, 50)
    }),
    created_at: serviceDate.toISOString(),
    updated_at: new Date().toISOString()
  });
});

// Generate tasks
console.log('Generating tasks...');
providers.forEach(provider => {
  const taskCount = randomInt(5, 15);
  for (let i = 0; i < taskCount; i++) {
    const createdAt = randomDate(new Date(2024, 0, 1), new Date());
    const dueDate = randomDate(createdAt, new Date(2024, 11, 31));
    const status = dueDate < new Date() ? 
      randomElement(['completed', 'cancelled']) : 
      randomElement(['pending', 'in_progress']);
    
    tasks.push({
      id: generateUUID(),
      organization_id: provider.organization_id,
      department_id: provider.department_id,
      title: randomElement([
        'Review lab results', 'Follow up with patient', 'Complete medical records',
        'Insurance authorization', 'Schedule procedure', 'Patient callback',
        'Medication review', 'Referral coordination'
      ]),
      description: faker.lorem.sentence(),
      priority: randomElement(['low', 'medium', 'high', 'urgent']),
      status: status,
      assigned_to: provider.user_id,
      assigned_by: provider.user_id,
      due_date: dueDate.toISOString(),
      completed_at: status === 'completed' ? randomDate(createdAt, dueDate).toISOString() : null,
      related_to: JSON.stringify({
        type: 'patient',
        id: randomElement(patients.filter(p => p.organization_id === provider.organization_id)).id
      }),
      metadata: JSON.stringify({
        priority_reason: faker.lorem.sentence()
      }),
      created_at: createdAt.toISOString(),
      updated_at: new Date().toISOString()
    });
  }
});

// Generate allergies
console.log('Generating allergies...');
patients.forEach(patient => {
  if (faker.datatype.boolean({ probability: 0.4 })) { // 40% of patients have allergies
    const allergyCount = randomInt(1, 3);
    const allergens = ['Penicillin', 'Peanuts', 'Latex', 'Shellfish', 'Iodine', 'Dust mites', 'Pollen', 'Eggs', 'Milk'];
    for (let i = 0; i < allergyCount; i++) {
      const provider = providers.find(p => p.organization_id === patient.organization_id) || providers[0];
      allergies.push({
        id: generateUUID(),
        patient_id: patient.id,
        allergen: randomElement(allergens),
        allergen_type: randomElement(['medication', 'food', 'environmental', 'other']),
        severity: randomElement(['mild', 'moderate', 'severe', 'life_threatening']),
        reaction: faker.lorem.sentence(),
        onset_date: randomDate(new Date(2020, 0, 1), new Date()).toISOString().split('T')[0],
        status: randomElement(['active', 'inactive', 'resolved']),
        reported_by: provider.id,
        notes: faker.datatype.boolean({ probability: 0.3 }) ? faker.lorem.sentence() : null,
        created_at: randomDate(new Date(2020, 0, 1), new Date()).toISOString(),
        updated_at: new Date().toISOString()
      });
    }
  }
});

// Generate care team members
console.log('Generating care team members...');
patients.forEach(patient => {
  const teamSize = randomInt(1, 4);
  const orgProviders = providers.filter(p => p.organization_id === patient.organization_id);
  for (let i = 0; i < teamSize; i++) {
    const provider = randomElement(orgProviders);
    careTeamMembers.push({
      id: generateUUID(),
      patient_id: patient.id,
      provider_id: provider.id,
      role: randomElement(['primary_care', 'specialist', 'nurse', 'care_coordinator']),
      specialization: provider.specialization,
      primary_contact: i === 0,
      contact_preference: randomElement(['phone', 'email', 'secure_message']),
      start_date: randomDate(new Date(2020, 0, 1), new Date()).toISOString().split('T')[0],
      end_date: faker.datatype.boolean({ probability: 0.1 }) ? randomDate(new Date(), new Date(2025, 0, 1)).toISOString().split('T')[0] : null,
      notes: faker.datatype.boolean({ probability: 0.2 }) ? faker.lorem.sentence() : null,
      created_at: randomDate(new Date(2020, 0, 1), new Date()).toISOString(),
      updated_at: new Date().toISOString()
    });
  }
});

// Generate clinical notes
console.log('Generating clinical notes...');
medicalRecords.forEach(record => {
  if (faker.datatype.boolean({ probability: 0.7 })) { // 70% of records have notes
    clinicalNotes.push({
      id: generateUUID(),
      medical_record_id: record.id,
      note_type: randomElement(['progress', 'assessment', 'plan', 'discharge']),
      content: faker.lorem.paragraphs(2),
      signed: faker.datatype.boolean({ probability: 0.8 }),
      signed_by: record.provider_id,
      signed_at: faker.datatype.boolean({ probability: 0.8 }) ? randomDate(new Date(record.visit_date), new Date()).toISOString() : null,
      created_at: record.visit_date,
      updated_at: new Date().toISOString()
    });
  }
});

// Generate conversations and messages
console.log('Generating conversations and messages...');
organizations.forEach(org => {
  const orgProviders = providers.filter(p => p.organization_id === org.id);
  const orgPatients = patients.filter(p => p.organization_id === org.id);
  
  // Provider-patient conversations
  for (let i = 0; i < randomInt(20, 50); i++) {
    const provider = randomElement(orgProviders);
    const patient = randomElement(orgPatients);
    const conversation = {
      id: generateUUID(),
      organization_id: org.id,
      type: 'direct',
      title: `Consultation with ${patient.first_name} ${patient.last_name}`,
      created_by: provider.user_id,
      metadata: JSON.stringify({
        patient_id: patient.id,
        provider_id: provider.id
      }),
      created_at: randomDate(new Date(2023, 0, 1), new Date()).toISOString(),
      updated_at: new Date().toISOString()
    };
    conversations.push(conversation);
    
    // Add participants
    conversationParticipants.push({
      id: generateUUID(),
      conversation_id: conversation.id,
      user_id: provider.user_id,
      role: 'provider',
      joined_at: conversation.created_at,
      last_read_at: randomDate(new Date(conversation.created_at), new Date()).toISOString()
    });
    
    conversationParticipants.push({
      id: generateUUID(),
      conversation_id: conversation.id,
      user_id: patient.user_id,
      role: 'patient',
      joined_at: conversation.created_at,
      last_read_at: randomDate(new Date(conversation.created_at), new Date()).toISOString()
    });
    
    // Add messages
    const messageCount = randomInt(2, 8);
    for (let j = 0; j < messageCount; j++) {
      const sender = j % 2 === 0 ? provider.user_id : patient.user_id;
      messages.push({
        id: generateUUID(),
        conversation_id: conversation.id,
        sender_id: sender,
        content: faker.lorem.sentences(randomInt(1, 3)),
        attachments: JSON.stringify([]),
        metadata: JSON.stringify({}),
        created_at: randomDate(new Date(conversation.created_at), new Date()).toISOString(),
        updated_at: new Date().toISOString()
      });
    }
  }
});

// Generate documents
console.log('Generating documents...');
patients.forEach(patient => {
  const docCount = randomInt(1, 5);
  for (let i = 0; i < docCount; i++) {
    const provider = providers.find(p => p.organization_id === patient.organization_id) || providers[0];
    documents.push({
      id: generateUUID(),
      patient_id: patient.id,
      organization_id: patient.organization_id,
      document_type: randomElement(['lab_report', 'imaging', 'consent', 'insurance', 'referral']),
      title: `${randomElement(['Lab Report', 'Imaging Study', 'Consent Form', 'Insurance Document', 'Referral'])} - ${patient.first_name} ${patient.last_name}`,
      file_url: `https://storage.example.com/documents/${generateUUID()}.pdf`,
      file_size: randomInt(100000, 5000000),
      mime_type: 'application/pdf',
      created_by: provider.user_id,
      metadata: JSON.stringify({
        patient_name: `${patient.first_name} ${patient.last_name}`,
        provider_name: `${provider.first_name} ${provider.last_name}`
      }),
      created_at: randomDate(new Date(2023, 0, 1), new Date()).toISOString(),
      updated_at: new Date().toISOString()
    });
  }
});

// Generate patient alerts
console.log('Generating patient alerts...');
patients.forEach(patient => {
  if (faker.datatype.boolean({ probability: 0.3 })) { // 30% of patients have alerts
    const alertCount = randomInt(1, 3);
    for (let i = 0; i < alertCount; i++) {
      const provider = providers.find(p => p.organization_id === patient.organization_id) || providers[0];
      patientAlerts.push({
        id: generateUUID(),
        patient_id: patient.id,
        alert_type: randomElement(['allergy', 'medication', 'condition', 'fall_risk', 'infection_control']),
        severity: randomElement(['low', 'medium', 'high', 'critical']),
        message: faker.lorem.sentence(),
        status: randomElement(['active', 'inactive', 'resolved']),
        expires_at: faker.datatype.boolean({ probability: 0.5 }) ? randomDate(new Date(), new Date(2025, 0, 1)).toISOString() : null,
        created_by: provider.user_id,
        acknowledged_by: faker.datatype.boolean({ probability: 0.6 }) ? provider.user_id : null,
        acknowledged_at: faker.datatype.boolean({ probability: 0.6 }) ? randomDate(new Date(2023, 0, 1), new Date()).toISOString() : null,
        metadata: JSON.stringify({
          priority: randomElement(['routine', 'urgent', 'emergency'])
        }),
        created_at: randomDate(new Date(2023, 0, 1), new Date()).toISOString(),
        updated_at: new Date().toISOString()
      });
    }
  }
});

// Generate patient portal settings
console.log('Generating patient portal settings...');
patients.forEach(patient => {
  patientPortalSettings.push({
    id: generateUUID(),
    patient_id: patient.id,
    email_notifications: faker.datatype.boolean({ probability: 0.8 }),
    sms_notifications: faker.datatype.boolean({ probability: 0.5 }),
    appointment_reminders: faker.datatype.boolean({ probability: 0.9 }),
    lab_result_notifications: faker.datatype.boolean({ probability: 0.7 }),
    created_at: randomDate(new Date(2023, 0, 1), new Date()).toISOString(),
    updated_at: new Date().toISOString()
  });
});

// Generate referrals
console.log('Generating referrals...');
patients.forEach(patient => {
  if (faker.datatype.boolean({ probability: 0.2 })) { // 20% of patients have referrals
    const referralCount = randomInt(1, 2);
    const orgProviders = providers.filter(p => p.organization_id === patient.organization_id);
    for (let i = 0; i < referralCount; i++) {
      const referringProvider = randomElement(orgProviders);
      const referredProvider = randomElement(providers.filter(p => p.id !== referringProvider.id));
      referrals.push({
        id: generateUUID(),
        patient_id: patient.id,
        referring_provider_id: referringProvider.id,
        referred_to_provider_id: referredProvider.id,
        reason: faker.lorem.sentence(),
        urgency: randomElement(['routine', 'urgent', 'emergency']),
        status: randomElement(['pending', 'scheduled', 'completed', 'cancelled']),
        priority: randomElement(['routine', 'urgent', 'emergency']),
        notes: faker.lorem.paragraph(),
        appointment_scheduled: faker.datatype.boolean({ probability: 0.6 }),
        appointment_date: faker.datatype.boolean({ probability: 0.6 }) ? randomDate(new Date(), new Date(2024, 11, 31)).toISOString() : null,
        created_at: randomDate(new Date(2023, 0, 1), new Date()).toISOString(),
        updated_at: new Date().toISOString()
      });
    }
  }
});

// Generate orders
console.log('Generating orders...');
patients.forEach(patient => {
  const orderCount = randomInt(0, 4);
  for (let i = 0; i < orderCount; i++) {
    const provider = providers.find(p => p.organization_id === patient.organization_id) || providers[0];
    orders.push({
      id: generateUUID(),
      patient_id: patient.id,
      ordering_provider_id: provider.id,
      order_type: randomElement(['lab', 'imaging', 'medication', 'procedure']),
      description: faker.lorem.sentence(),
      instructions: faker.lorem.paragraph(),
      priority: randomElement(['routine', 'urgent', 'stat', 'emergency']),
      status: randomElement(['pending', 'approved', 'in_progress', 'completed', 'cancelled']),
      ordered_at: randomDate(new Date(2023, 0, 1), new Date()).toISOString(),
      scheduled_date: faker.datatype.boolean({ probability: 0.7 }) ? randomDate(new Date(), new Date(2024, 11, 31)).toISOString() : null,
      completed_at: faker.datatype.boolean({ probability: 0.4 }) ? randomDate(new Date(2023, 6, 1), new Date()).toISOString() : null,
      metadata: JSON.stringify({
        department: randomElement(['lab', 'radiology', 'pharmacy', 'cardiology']),
        estimated_duration: randomInt(15, 120)
      }),
      created_at: randomDate(new Date(2023, 0, 1), new Date()).toISOString(),
      updated_at: new Date().toISOString()
    });
  }
});

// Generate teams
console.log('Generating teams...');
departments.forEach(dept => {
  teams.push({
    id: generateUUID(),
    department_id: dept.id,
    name: `${dept.name} Team`,
    description: `Clinical team for ${dept.name} department`,
    leader_id: randomElement(providers.filter(p => p.department_id === dept.id))?.user_id || generateUUID(),
    members: JSON.stringify(providers.filter(p => p.department_id === dept.id).map(p => p.user_id)),
    settings: JSON.stringify({
      meeting_schedule: 'Weekly',
      communication_channel: 'secure_messaging'
    }),
    created_at: randomDate(new Date(2023, 0, 1), new Date()).toISOString(),
    updated_at: new Date().toISOString()
  });
});

// Generate notification templates
console.log('Generating notification templates...');
organizations.forEach(org => {
  const templateTypes = [
    { type: 'appointment_reminder', name: 'Appointment Reminder', subject: 'Upcoming Appointment Reminder', content: 'You have an appointment scheduled for {{appointment_date}} with {{provider_name}}.' },
    { type: 'lab_result', name: 'Lab Results Available', subject: 'Your Lab Results Are Ready', content: 'Your lab results from {{test_date}} are now available in your patient portal.' },
    { type: 'prescription_update', name: 'Prescription Update', subject: 'Prescription Update', content: 'Your prescription for {{medication_name}} has been updated.' },
    { type: 'medical_record_update', name: 'Medical Record Update', subject: 'Medical Record Updated', content: 'Your medical record has been updated following your visit on {{visit_date}}.' }
  ];
  
  templateTypes.forEach(template => {
    notificationTemplates.push({
      id: generateUUID(),
      organization_id: org.id,
      type: template.type,
      name: template.name,
      subject_template: template.subject,
      content_template: template.content,
      metadata_template: JSON.stringify({
        priority: 'medium',
        delivery_methods: ['email', 'sms', 'push']
      }),
      created_at: randomDate(new Date(2023, 0, 1), new Date()).toISOString(),
      updated_at: new Date().toISOString()
    });
  });
});

// Generate workflows
console.log('Generating workflows...');
organizations.forEach(org => {
  const workflowTypes = [
    { type: 'appointment_reminder', name: 'Appointment Reminder Workflow', description: 'Automated appointment reminders' },
    { type: 'lab_result_notification', name: 'Lab Result Notification', description: 'Notify patients when lab results are available' },
    { type: 'prescription_renewal', name: 'Prescription Renewal', description: 'Handle prescription renewal requests' }
  ];
  
  workflowTypes.forEach(workflow => {
    const workflowId = generateUUID();
    workflows.push({
      id: workflowId,
      organization_id: org.id,
      name: workflow.name,
      description: workflow.description,
      type: workflow.type,
      trigger_type: randomElement(['scheduled', 'event_based']),
      is_active: faker.datatype.boolean({ probability: 0.8 }),
      configuration: JSON.stringify({
        steps: [
          { step: 1, action: 'check_condition', parameters: {} },
          { step: 2, action: 'send_notification', parameters: {} },
          { step: 3, action: 'log_completion', parameters: {} }
        ]
      }),
      created_by: randomElement(providers.filter(p => p.organization_id === org.id))?.user_id || generateUUID(),
      created_at: randomDate(new Date(2023, 0, 1), new Date()).toISOString(),
      updated_at: new Date().toISOString()
    });
  });
});

// Generate SQL output
console.log('Generating SQL file...');

let sql = `-- Comprehensive Healthcare Seed Data
-- Generated on ${new Date().toISOString()}
-- Organizations: ${organizations.length}
-- Total Patients: ${patients.length}
-- Total Appointments: ${appointments.length}

`;

// Helper function to escape SQL values
const escapeSqlValue = (value) => {
  if (value === null || value === undefined) return 'NULL';
  if (typeof value === 'string') {
    return `'${value.replace(/'/g, "''")}'`;
  }
  if (typeof value === 'boolean') return value ? 'true' : 'false';
  if (typeof value === 'object') return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
  return value;
};

// Organizations
if (organizations.length > 0) {
  sql += `-- Organizations (${organizations.length} records)\n`;
  sql += `INSERT INTO public.organizations (id, name, type, settings, created_at, updated_at) VALUES\n`;
  sql += organizations.map(org => 
    `(${escapeSqlValue(org.id)}, ${escapeSqlValue(org.name)}, ${escapeSqlValue(org.type)}, ${escapeSqlValue(org.settings)}, ${escapeSqlValue(org.created_at)}, ${escapeSqlValue(org.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Facilities  
if (facilities.length > 0) {
  sql += `-- Facilities (${facilities.length} records)\n`;
  sql += `INSERT INTO public.facilities (id, organization_id, name, type, address, contact_info, operating_hours, settings, created_at, updated_at) VALUES\n`;
  sql += facilities.map(facility => 
    `(${escapeSqlValue(facility.id)}, ${escapeSqlValue(facility.organization_id)}, ${escapeSqlValue(facility.name)}, ${escapeSqlValue(facility.type)}, ${escapeSqlValue(facility.address)}, ${escapeSqlValue(facility.contact_info)}, ${escapeSqlValue(facility.operating_hours)}, ${escapeSqlValue(facility.settings)}, ${escapeSqlValue(facility.created_at)}, ${escapeSqlValue(facility.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Departments
if (departments.length > 0) {
  sql += `-- Departments (${departments.length} records)\n`;
  sql += `INSERT INTO public.departments (id, facility_id, name, type, settings, created_at, updated_at) VALUES\n`;
  sql += departments.map(dept => 
    `(${escapeSqlValue(dept.id)}, ${escapeSqlValue(dept.facility_id)}, ${escapeSqlValue(dept.name)}, ${escapeSqlValue(dept.type)}, ${escapeSqlValue(dept.settings)}, ${escapeSqlValue(dept.created_at)}, ${escapeSqlValue(dept.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Healthcare Providers
if (providers.length > 0) {
  sql += `-- Healthcare Providers (${providers.length} records)\n`;
  sql += `INSERT INTO public.healthcare_providers (id, user_id, first_name, last_name, provider_type, specialization, license_number, created_at, updated_at, organization_id, department_id, role, specialties, credentials, schedule_settings, permissions) VALUES\n`;
  sql += providers.map(provider => 
    `(${escapeSqlValue(provider.id)}, ${escapeSqlValue(provider.user_id)}, ${escapeSqlValue(provider.first_name)}, ${escapeSqlValue(provider.last_name)}, ${escapeSqlValue(provider.provider_type)}, ${escapeSqlValue(provider.specialization)}, ${escapeSqlValue(provider.license_number)}, ${escapeSqlValue(provider.created_at)}, ${escapeSqlValue(provider.updated_at)}, ${escapeSqlValue(provider.organization_id)}, ${escapeSqlValue(provider.department_id)}, ${escapeSqlValue(provider.role)}, ARRAY[${provider.specialties.map(s => escapeSqlValue(s)).join(',')}], ${escapeSqlValue(provider.credentials)}, ${escapeSqlValue(provider.schedule_settings)}, ${escapeSqlValue(provider.permissions)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Patients
if (patients.length > 0) {
  sql += `-- Patients (${patients.length} records)\n`;
  sql += `INSERT INTO public.patients (id, organization_id, user_id, first_name, last_name, email, phone, date_of_birth, gender, address, city, state, zip_code, emergency_contact, insurance_info, metadata, created_at, updated_at) VALUES\n`;
  sql += patients.map(patient => 
    `(${escapeSqlValue(patient.id)}, ${escapeSqlValue(patient.organization_id)}, ${escapeSqlValue(patient.user_id)}, ${escapeSqlValue(patient.first_name)}, ${escapeSqlValue(patient.last_name)}, ${escapeSqlValue(patient.email)}, ${escapeSqlValue(patient.phone)}, ${escapeSqlValue(patient.date_of_birth)}, ${escapeSqlValue(patient.gender)}, ${escapeSqlValue(patient.address)}, ${escapeSqlValue(patient.city)}, ${escapeSqlValue(patient.state)}, ${escapeSqlValue(patient.zip_code)}, ${escapeSqlValue(patient.emergency_contact)}, ${escapeSqlValue(patient.insurance_info)}, ${escapeSqlValue(patient.metadata)}, ${escapeSqlValue(patient.created_at)}, ${escapeSqlValue(patient.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Appointments
if (appointments.length > 0) {
  sql += `-- Appointments (${appointments.length} records)\n`;
  sql += `INSERT INTO public.appointments (id, patient_id, provider_id, organization_id, department_id, appointment_date, duration_minutes, status, reason, notes, created_at, updated_at) VALUES\n`;
  sql += appointments.map(appointment => 
    `(${escapeSqlValue(appointment.id)}, ${escapeSqlValue(appointment.patient_id)}, ${escapeSqlValue(appointment.provider_id)}, ${escapeSqlValue(appointment.organization_id)}, ${escapeSqlValue(appointment.department_id)}, ${escapeSqlValue(appointment.appointment_date)}, ${escapeSqlValue(appointment.duration_minutes)}, ${escapeSqlValue(appointment.status)}, ${escapeSqlValue(appointment.reason)}, ${escapeSqlValue(appointment.notes)}, ${escapeSqlValue(appointment.created_at)}, ${escapeSqlValue(appointment.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Medical Records
if (medicalRecords.length > 0) {
  sql += `-- Medical Records (${medicalRecords.length} records)\n`;
  sql += `INSERT INTO public.medical_records (id, patient_id, provider_id, organization_id, department_id, visit_date, chief_complaint, diagnosis, treatment_plan, notes, attachments, created_at, updated_at) VALUES\n`;
  sql += medicalRecords.map(record => 
    `(${escapeSqlValue(record.id)}, ${escapeSqlValue(record.patient_id)}, ${escapeSqlValue(record.provider_id)}, ${escapeSqlValue(record.organization_id)}, ${escapeSqlValue(record.department_id)}, ${escapeSqlValue(record.visit_date)}, ${escapeSqlValue(record.chief_complaint)}, ARRAY[${record.diagnosis.map(d => escapeSqlValue(d)).join(',')}], ${escapeSqlValue(record.treatment_plan)}, ${escapeSqlValue(record.notes)}, ${escapeSqlValue(record.attachments)}, ${escapeSqlValue(record.created_at)}, ${escapeSqlValue(record.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Medications
if (medications.length > 0) {
  sql += `-- Medications (${medications.length} records)\n`;
  sql += `INSERT INTO public.medications (id, patient_id, provider_id, medication_name, dosage, frequency, start_date, end_date, instructions, active, created_at, updated_at) VALUES\n`;
  sql += medications.map(med => 
    `(${escapeSqlValue(med.id)}, ${escapeSqlValue(med.patient_id)}, ${escapeSqlValue(med.provider_id)}, ${escapeSqlValue(med.medication_name)}, ${escapeSqlValue(med.dosage)}, ${escapeSqlValue(med.frequency)}, ${escapeSqlValue(med.start_date)}, ${escapeSqlValue(med.end_date)}, ${escapeSqlValue(med.instructions)}, ${escapeSqlValue(med.active)}, ${escapeSqlValue(med.created_at)}, ${escapeSqlValue(med.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Vital Signs
if (vitalSigns.length > 0) {
  sql += `-- Vital Signs (${vitalSigns.length} records)\n`;
  sql += `INSERT INTO public.vital_signs (id, patient_id, recorded_by, recorded_at, blood_pressure_systolic, blood_pressure_diastolic, heart_rate, respiratory_rate, temperature, temperature_unit, oxygen_saturation, height, weight, bmi, pain_level, notes, metadata, created_at, updated_at) VALUES\n`;
  sql += vitalSigns.map(vital => 
    `(${escapeSqlValue(vital.id)}, ${escapeSqlValue(vital.patient_id)}, ${escapeSqlValue(vital.recorded_by)}, ${escapeSqlValue(vital.recorded_at)}, ${escapeSqlValue(vital.blood_pressure_systolic)}, ${escapeSqlValue(vital.blood_pressure_diastolic)}, ${escapeSqlValue(vital.heart_rate)}, ${escapeSqlValue(vital.respiratory_rate)}, ${escapeSqlValue(vital.temperature)}, ${escapeSqlValue(vital.temperature_unit)}, ${escapeSqlValue(vital.oxygen_saturation)}, ${escapeSqlValue(vital.height)}, ${escapeSqlValue(vital.weight)}, ${escapeSqlValue(vital.bmi)}, ${escapeSqlValue(vital.pain_level)}, ${escapeSqlValue(vital.notes)}, ${escapeSqlValue(vital.metadata)}, ${escapeSqlValue(vital.created_at)}, ${escapeSqlValue(vital.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Lab Results
if (labResults.length > 0) {
  sql += `-- Lab Results (${labResults.length} records)\n`;
  sql += `INSERT INTO public.lab_results (id, patient_id, provider_id, test_name, test_date, results, normal_range, notes, created_at, updated_at) VALUES\n`;
  sql += labResults.map(lab => 
    `(${escapeSqlValue(lab.id)}, ${escapeSqlValue(lab.patient_id)}, ${escapeSqlValue(lab.provider_id)}, ${escapeSqlValue(lab.test_name)}, ${escapeSqlValue(lab.test_date)}, ${escapeSqlValue(lab.results)}, ${escapeSqlValue(lab.normal_range)}, ${escapeSqlValue(lab.notes)}, ${escapeSqlValue(lab.created_at)}, ${escapeSqlValue(lab.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Immunizations
if (immunizations.length > 0) {
  sql += `-- Immunizations (${immunizations.length} records)\n`;
  sql += `INSERT INTO public.immunizations (id, patient_id, administered_by, vaccine_name, vaccine_code, manufacturer, lot_number, administered_date, expiration_date, site, route, dosage, notes, metadata, created_at, updated_at) VALUES\n`;
  sql += immunizations.map(imm => 
    `(${escapeSqlValue(imm.id)}, ${escapeSqlValue(imm.patient_id)}, ${escapeSqlValue(imm.administered_by)}, ${escapeSqlValue(imm.vaccine_name)}, ${escapeSqlValue(imm.vaccine_code)}, ${escapeSqlValue(imm.manufacturer)}, ${escapeSqlValue(imm.lot_number)}, ${escapeSqlValue(imm.administered_date)}, ${escapeSqlValue(imm.expiration_date)}, ${escapeSqlValue(imm.site)}, ${escapeSqlValue(imm.route)}, ${escapeSqlValue(imm.dosage)}, ${escapeSqlValue(imm.notes)}, ${escapeSqlValue(imm.metadata)}, ${escapeSqlValue(imm.created_at)}, ${escapeSqlValue(imm.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Claims
if (claims.length > 0) {
  sql += `-- Claims (${claims.length} records)\n`;
  sql += `INSERT INTO public.claims (id, patient_id, provider_id, insurance_provider_id, service_date, billing_codes, status, amount, submitted_at, processed_at, metadata, created_at, updated_at) VALUES\n`;
  sql += claims.map(claim => 
    `(${escapeSqlValue(claim.id)}, ${escapeSqlValue(claim.patient_id)}, ${escapeSqlValue(claim.provider_id)}, ${escapeSqlValue(claim.insurance_provider_id)}, ${escapeSqlValue(claim.service_date)}, ${escapeSqlValue(claim.billing_codes)}, ${escapeSqlValue(claim.status)}, ${escapeSqlValue(claim.amount)}, ${escapeSqlValue(claim.submitted_at)}, ${escapeSqlValue(claim.processed_at)}, ${escapeSqlValue(claim.metadata)}, ${escapeSqlValue(claim.created_at)}, ${escapeSqlValue(claim.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Tasks
if (tasks.length > 0) {
  sql += `-- Tasks (${tasks.length} records)\n`;
  sql += `INSERT INTO public.tasks (id, organization_id, department_id, title, description, priority, status, assigned_to, assigned_by, due_date, completed_at, related_to, metadata, created_at, updated_at) VALUES\n`;
  sql += tasks.map(task => 
    `(${escapeSqlValue(task.id)}, ${escapeSqlValue(task.organization_id)}, ${escapeSqlValue(task.department_id)}, ${escapeSqlValue(task.title)}, ${escapeSqlValue(task.description)}, ${escapeSqlValue(task.priority)}, ${escapeSqlValue(task.status)}, ${escapeSqlValue(task.assigned_to)}, ${escapeSqlValue(task.assigned_by)}, ${escapeSqlValue(task.due_date)}, ${escapeSqlValue(task.completed_at)}, ${escapeSqlValue(task.related_to)}, ${escapeSqlValue(task.metadata)}, ${escapeSqlValue(task.created_at)}, ${escapeSqlValue(task.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Allergies
if (allergies.length > 0) {
  sql += `-- Allergies (${allergies.length} records)\n`;
  sql += `INSERT INTO public.allergies (id, patient_id, allergen, allergen_type, severity, reaction, onset_date, status, reported_by, notes, created_at, updated_at) VALUES\n`;
  sql += allergies.map(allergy => 
    `(${escapeSqlValue(allergy.id)}, ${escapeSqlValue(allergy.patient_id)}, ${escapeSqlValue(allergy.allergen)}, ${escapeSqlValue(allergy.allergen_type)}, ${escapeSqlValue(allergy.severity)}, ${escapeSqlValue(allergy.reaction)}, ${escapeSqlValue(allergy.onset_date)}, ${escapeSqlValue(allergy.status)}, ${escapeSqlValue(allergy.reported_by)}, ${escapeSqlValue(allergy.notes)}, ${escapeSqlValue(allergy.created_at)}, ${escapeSqlValue(allergy.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Care Team Members
if (careTeamMembers.length > 0) {
  sql += `-- Care Team Members (${careTeamMembers.length} records)\n`;
  sql += `INSERT INTO public.care_team_members (id, patient_id, provider_id, role, specialization, primary_contact, contact_preference, start_date, end_date, notes, created_at, updated_at) VALUES\n`;
  sql += careTeamMembers.map(member => 
    `(${escapeSqlValue(member.id)}, ${escapeSqlValue(member.patient_id)}, ${escapeSqlValue(member.provider_id)}, ${escapeSqlValue(member.role)}, ${escapeSqlValue(member.specialization)}, ${escapeSqlValue(member.primary_contact)}, ${escapeSqlValue(member.contact_preference)}, ${escapeSqlValue(member.start_date)}, ${escapeSqlValue(member.end_date)}, ${escapeSqlValue(member.notes)}, ${escapeSqlValue(member.created_at)}, ${escapeSqlValue(member.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Clinical Notes
if (clinicalNotes.length > 0) {
  sql += `-- Clinical Notes (${clinicalNotes.length} records)\n`;
  sql += `INSERT INTO public.clinical_notes (id, medical_record_id, note_type, content, signed, signed_by, signed_at, created_at, updated_at) VALUES\n`;
  sql += clinicalNotes.map(note => 
    `(${escapeSqlValue(note.id)}, ${escapeSqlValue(note.medical_record_id)}, ${escapeSqlValue(note.note_type)}, ${escapeSqlValue(note.content)}, ${escapeSqlValue(note.signed)}, ${escapeSqlValue(note.signed_by)}, ${escapeSqlValue(note.signed_at)}, ${escapeSqlValue(note.created_at)}, ${escapeSqlValue(note.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Conversations
if (conversations.length > 0) {
  sql += `-- Conversations (${conversations.length} records)\n`;
  sql += `INSERT INTO public.conversations (id, organization_id, type, title, created_by, metadata, created_at, updated_at) VALUES\n`;
  sql += conversations.map(conversation => 
    `(${escapeSqlValue(conversation.id)}, ${escapeSqlValue(conversation.organization_id)}, ${escapeSqlValue(conversation.type)}, ${escapeSqlValue(conversation.title)}, ${escapeSqlValue(conversation.created_by)}, ${escapeSqlValue(conversation.metadata)}, ${escapeSqlValue(conversation.created_at)}, ${escapeSqlValue(conversation.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Conversation Participants
if (conversationParticipants.length > 0) {
  sql += `-- Conversation Participants (${conversationParticipants.length} records)\n`;
  sql += `INSERT INTO public.conversation_participants (id, conversation_id, user_id, role, joined_at, last_read_at) VALUES\n`;
  sql += conversationParticipants.map(participant => 
    `(${escapeSqlValue(participant.id)}, ${escapeSqlValue(participant.conversation_id)}, ${escapeSqlValue(participant.user_id)}, ${escapeSqlValue(participant.role)}, ${escapeSqlValue(participant.joined_at)}, ${escapeSqlValue(participant.last_read_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Messages
if (messages.length > 0) {
  sql += `-- Messages (${messages.length} records)\n`;
  sql += `INSERT INTO public.messages (id, conversation_id, sender_id, content, attachments, metadata, created_at, updated_at) VALUES\n`;
  sql += messages.map(message => 
    `(${escapeSqlValue(message.id)}, ${escapeSqlValue(message.conversation_id)}, ${escapeSqlValue(message.sender_id)}, ${escapeSqlValue(message.content)}, ${escapeSqlValue(message.attachments)}, ${escapeSqlValue(message.metadata)}, ${escapeSqlValue(message.created_at)}, ${escapeSqlValue(message.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Patient Alerts
if (patientAlerts.length > 0) {
  sql += `-- Patient Alerts (${patientAlerts.length} records)\n`;
  sql += `INSERT INTO public.patient_alerts (id, patient_id, alert_type, severity, message, status, expires_at, created_by, acknowledged_by, acknowledged_at, metadata, created_at, updated_at) VALUES\n`;
  sql += patientAlerts.map(alert => 
    `(${escapeSqlValue(alert.id)}, ${escapeSqlValue(alert.patient_id)}, ${escapeSqlValue(alert.alert_type)}, ${escapeSqlValue(alert.severity)}, ${escapeSqlValue(alert.message)}, ${escapeSqlValue(alert.status)}, ${escapeSqlValue(alert.expires_at)}, ${escapeSqlValue(alert.created_by)}, ${escapeSqlValue(alert.acknowledged_by)}, ${escapeSqlValue(alert.acknowledged_at)}, ${escapeSqlValue(alert.metadata)}, ${escapeSqlValue(alert.created_at)}, ${escapeSqlValue(alert.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Patient Portal Settings
if (patientPortalSettings.length > 0) {
  sql += `-- Patient Portal Settings (${patientPortalSettings.length} records)\n`;
  sql += `INSERT INTO public.patient_portal_settings (id, patient_id, email_notifications, sms_notifications, appointment_reminders, lab_result_notifications, created_at, updated_at) VALUES\n`;
  sql += patientPortalSettings.map(setting => 
    `(${escapeSqlValue(setting.id)}, ${escapeSqlValue(setting.patient_id)}, ${escapeSqlValue(setting.email_notifications)}, ${escapeSqlValue(setting.sms_notifications)}, ${escapeSqlValue(setting.appointment_reminders)}, ${escapeSqlValue(setting.lab_result_notifications)}, ${escapeSqlValue(setting.created_at)}, ${escapeSqlValue(setting.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Referrals
if (referrals.length > 0) {
  sql += `-- Referrals (${referrals.length} records)\n`;
  sql += `INSERT INTO public.referrals (id, patient_id, referring_provider_id, referred_to_provider_id, reason, urgency, status, priority, notes, appointment_scheduled, appointment_date, created_at, updated_at) VALUES\n`;
  sql += referrals.map(referral => 
    `(${escapeSqlValue(referral.id)}, ${escapeSqlValue(referral.patient_id)}, ${escapeSqlValue(referral.referring_provider_id)}, ${escapeSqlValue(referral.referred_to_provider_id)}, ${escapeSqlValue(referral.reason)}, ${escapeSqlValue(referral.urgency)}, ${escapeSqlValue(referral.status)}, ${escapeSqlValue(referral.priority)}, ${escapeSqlValue(referral.notes)}, ${escapeSqlValue(referral.appointment_scheduled)}, ${escapeSqlValue(referral.appointment_date)}, ${escapeSqlValue(referral.created_at)}, ${escapeSqlValue(referral.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Orders
if (orders.length > 0) {
  sql += `-- Orders (${orders.length} records)\n`;
  sql += `INSERT INTO public.orders (id, patient_id, ordering_provider_id, order_type, description, instructions, priority, status, ordered_at, scheduled_date, completed_at, metadata, created_at, updated_at) VALUES\n`;
  sql += orders.map(order => 
    `(${escapeSqlValue(order.id)}, ${escapeSqlValue(order.patient_id)}, ${escapeSqlValue(order.ordering_provider_id)}, ${escapeSqlValue(order.order_type)}, ${escapeSqlValue(order.description)}, ${escapeSqlValue(order.instructions)}, ${escapeSqlValue(order.priority)}, ${escapeSqlValue(order.status)}, ${escapeSqlValue(order.ordered_at)}, ${escapeSqlValue(order.scheduled_date)}, ${escapeSqlValue(order.completed_at)}, ${escapeSqlValue(order.metadata)}, ${escapeSqlValue(order.created_at)}, ${escapeSqlValue(order.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Teams
if (teams.length > 0) {
  sql += `-- Teams (${teams.length} records)\n`;
  sql += `INSERT INTO public.teams (id, department_id, name, description, leader_id, members, settings, created_at, updated_at) VALUES\n`;
  sql += teams.map(team => 
    `(${escapeSqlValue(team.id)}, ${escapeSqlValue(team.department_id)}, ${escapeSqlValue(team.name)}, ${escapeSqlValue(team.description)}, ${escapeSqlValue(team.leader_id)}, ${escapeSqlValue(team.members)}, ${escapeSqlValue(team.settings)}, ${escapeSqlValue(team.created_at)}, ${escapeSqlValue(team.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Notification Templates
if (notificationTemplates.length > 0) {
  sql += `-- Notification Templates (${notificationTemplates.length} records)\n`;
  sql += `INSERT INTO public.notification_templates (id, organization_id, type, name, subject_template, content_template, metadata_template, created_at, updated_at) VALUES\n`;
  sql += notificationTemplates.map(template => 
    `(${escapeSqlValue(template.id)}, ${escapeSqlValue(template.organization_id)}, ${escapeSqlValue(template.type)}, ${escapeSqlValue(template.name)}, ${escapeSqlValue(template.subject_template)}, ${escapeSqlValue(template.content_template)}, ${escapeSqlValue(template.metadata_template)}, ${escapeSqlValue(template.created_at)}, ${escapeSqlValue(template.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Workflows
if (workflows.length > 0) {
  sql += `-- Workflows (${workflows.length} records)\n`;
  sql += `INSERT INTO public.workflows (id, organization_id, name, description, type, trigger_type, is_active, configuration, created_by, created_at, updated_at) VALUES\n`;
  sql += workflows.map(workflow => 
    `(${escapeSqlValue(workflow.id)}, ${escapeSqlValue(workflow.organization_id)}, ${escapeSqlValue(workflow.name)}, ${escapeSqlValue(workflow.description)}, ${escapeSqlValue(workflow.type)}, ${escapeSqlValue(workflow.trigger_type)}, ${escapeSqlValue(workflow.is_active)}, ${escapeSqlValue(workflow.configuration)}, ${escapeSqlValue(workflow.created_by)}, ${escapeSqlValue(workflow.created_at)}, ${escapeSqlValue(workflow.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Documents
if (documents.length > 0) {
  sql += `-- Documents (${documents.length} records)\n`;
  sql += `INSERT INTO public.documents (id, patient_id, organization_id, document_type, title, file_url, file_size, mime_type, created_by, metadata, created_at, updated_at) VALUES\n`;
  sql += documents.map(document => 
    `(${escapeSqlValue(document.id)}, ${escapeSqlValue(document.patient_id)}, ${escapeSqlValue(document.organization_id)}, ${escapeSqlValue(document.document_type)}, ${escapeSqlValue(document.title)}, ${escapeSqlValue(document.file_url)}, ${escapeSqlValue(document.file_size)}, ${escapeSqlValue(document.mime_type)}, ${escapeSqlValue(document.created_by)}, ${escapeSqlValue(document.metadata)}, ${escapeSqlValue(document.created_at)}, ${escapeSqlValue(document.updated_at)})`
  ).join(',\n');
  sql += ';\n\n';
}

// Write to file
fs.writeFileSync('supabase/seeds/environments/development/99_comprehensive_data.sql', sql);

console.log('\n🎉 Comprehensive seed data generated successfully!');
console.log(`📊 Data Summary:
- Organizations: ${organizations.length}
- Facilities: ${facilities.length}  
- Departments: ${departments.length}
- Healthcare Providers: ${providers.length}
- Patients: ${patients.length}
- Appointments: ${appointments.length}
- Medical Records: ${medicalRecords.length}
- Medications: ${medications.length}
- Vital Signs: ${vitalSigns.length}
- Lab Results: ${labResults.length}
- Immunizations: ${immunizations.length}
- Claims: ${claims.length}
- Tasks: ${tasks.length}
- Allergies: ${allergies.length}
- Care Team Members: ${careTeamMembers.length}
- Clinical Notes: ${clinicalNotes.length}
- Conversations: ${conversations.length}
- Conversation Participants: ${conversationParticipants.length}
- Messages: ${messages.length}
- Documents: ${documents.length}
- Patient Alerts: ${patientAlerts.length}
- Patient Portal Settings: ${patientPortalSettings.length}
- Referrals: ${referrals.length}
- Orders: ${orders.length}
- Teams: ${teams.length}
- Notification Templates: ${notificationTemplates.length}
- Workflows: ${workflows.length}

📄 SQL file: supabase/seeds/environments/development/99_comprehensive_data.sql
📏 File size: ${(fs.statSync('supabase/seeds/environments/development/99_comprehensive_data.sql').size / 1024 / 1024).toFixed(2)} MB
`);
